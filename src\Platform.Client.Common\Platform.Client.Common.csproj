﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFrameworks>net9.0-android</TargetFrameworks>
		<UseMaui>true</UseMaui>
		<SingleProject>true</SingleProject>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>

		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">21.0</SupportedOSPlatformVersion>

		<LangVersion>preview</LangVersion>
	</PropertyGroup>

	<ItemGroup> 
		<PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="9.0.8" /> 
		<PackageReference Include="Microsoft.Maui.Controls" Version="9.0.100" />
		<PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="9.0.8" />
		<PackageReference Include="Microsoft.Extensions.Http" Version="9.0.8" />
		
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\DeepMessage.MauiShared\Platform.Framework.Maui.csproj" />
	  <ProjectReference Include="..\DeepMessage.ServiceContracts\DeepMessage.ServiceContracts.csproj" />
	  <ProjectReference Include="..\Platform.Client.Data\Platform.Client.Data.csproj" />
	  <ProjectReference Include="..\Platform.Client.Services\Platform.Client.Services.csproj" />
	</ItemGroup>

	<ItemGroup>
	  <Compile Update="Features\Account\SigninForm\FakeCaptchaScreen.xaml.cs">
	    <DependentUpon>FakeCaptchaScreen.xaml</DependentUpon>
	  </Compile>
	  <Compile Update="Features\Account\SigninForm\SignInFormComponent.xaml.cs">
	    <DependentUpon>SignInFormComponent.xaml</DependentUpon>
	  </Compile>
	</ItemGroup>

	<ItemGroup>
	  <None Update="Features\Account\Profile\Form\ProfileFormComponent.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </None>
	  <None Update="Features\Account\Profile\Listing\ProfileListingComponent.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </None>
	  <None Update="Features\Account\SigninForm\FakeCaptchaScreen.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </None>
	  <None Update="Features\Account\SigninForm\SignInFormComponent.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </None>
	  <None Update="Features\Account\SignupForm\SignupFormComponent.xaml">
	    <Generator>MSBuild:Compile</Generator>
	  </None>
	</ItemGroup>

	

</Project>
