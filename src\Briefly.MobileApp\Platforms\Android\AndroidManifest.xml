﻿<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android" android:versionName="2">
	<application android:allowBackup="true" android:icon="@mipmap/appicon" android:supportsRtl="true" android:usesCleartextTraffic="true">
		<!--<service
      android:name="DeepMessage.MauiApp.Platforms.Android.DeepSyncService"
      android:enabled="true"
      android:exported="true"
      android:foregroundServiceType="dataSync" />-->
		<service android:name="com.google.firebase.iid.FirebaseInstanceIdInternalReceiver" android:exported="false" />
		<receiver android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver" android:exported="true" android:permission="com.google.android.c2dm.permission.SEND">
			<intent-filter>
				<action android:name="com.google.android.c2dm.intent.RECEIVE" />
				<action android:name="com.google.android.c2dm.intent.REGISTRATION" />
				<category android:name="${applicationId}" />
			</intent-filter>
		</receiver>
	</application>
	<!-- ✅ ANDROID 9 COMPATIBILITY: Basic permissions that work on all API levels -->
	<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
	<uses-permission android:name="android.permission.INTERNET" />
	<uses-permission android:name="android.permission.CAMERA" />
	<uses-permission android:name="android.permission.VIBRATE" />

	<!-- ✅ ANDROID 9 COMPATIBILITY: Conditional permissions for newer Android versions -->
	<uses-permission android:name="android.permission.FOREGROUND_SERVICE" android:minSdkVersion="28" />
	<uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" android:minSdkVersion="34" />
	<uses-permission android:name="android.permission.POST_NOTIFICATIONS" android:minSdkVersion="33" />
</manifest>