﻿<Project Sdk="Microsoft.NET.Sdk.Razor">

  <PropertyGroup>
    <TargetFrameworks>net9.0-android;net9.0-ios;</TargetFrameworks>
  

    <OutputType>Exe</OutputType>
    <RootNamespace>ModelFury.Briefly.FluentHybridApp</RootNamespace>
    <UseMaui>true</UseMaui>
    <SingleProject>true</SingleProject>
    <ImplicitUsings>enable</ImplicitUsings>
    <EnableDefaultCssItems>false</EnableDefaultCssItems>
    <Nullable>enable</Nullable>

    <!-- Display name -->
    <ApplicationTitle>Briefly Fluent</ApplicationTitle>

    <!-- App Identifier -->
    <ApplicationId>com.companyname.modelfury.briefly.fluenthybridapp</ApplicationId>

    <!-- Versions -->
    <ApplicationDisplayVersion>1.0</ApplicationDisplayVersion>
    <ApplicationVersion>1</ApplicationVersion>

    <!-- To develop, package, and publish an app to the Microsoft Store, see: https://aka.ms/MauiTemplateUnpackaged -->
    <WindowsPackageType>None</WindowsPackageType>

    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'ios'">15.0</SupportedOSPlatformVersion>
    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">24.0</SupportedOSPlatformVersion>
  </PropertyGroup>

  <ItemGroup>
    <!-- App Icon -->
    <MauiIcon Include="Resources\AppIcon\appicon.svg" ForegroundFile="Resources\AppIcon\appiconfg.svg" Color="#512BD4" />

    <!-- Splash Screen -->
    <MauiSplashScreen Include="Resources\Splash\splash.svg" Color="#512BD4" BaseSize="128,128" />

    <!-- Images -->
    <MauiImage Include="Resources\Images\*" />
    <MauiImage Update="Resources\Images\dotnet_bot.svg" BaseSize="168,208" />

    <!-- Custom Fonts -->
    <MauiFont Include="Resources\Fonts\*" />

    <!-- Raw Assets (also remove the "Resources\Raw" prefix) -->
    <MauiAsset Include="Resources\Raw\**" LogicalName="%(RecursiveDir)%(Filename)%(Extension)" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Maui.Controls" Version="9.0.100" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebView.Maui" Version="9.0.100" />
    <PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="9.0.8" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.FluentUI.AspNetCore.Components" Version="4.12.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Platform.FluentRazor\Platform.FluentRazor.csproj" />
  </ItemGroup>

</Project>
