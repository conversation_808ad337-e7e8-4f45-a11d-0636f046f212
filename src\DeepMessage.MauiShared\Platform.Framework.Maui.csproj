﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFrameworks>net9.0-android;net9.0-ios</TargetFrameworks>
		<TargetFrameworks Condition="$([MSBuild]::IsOSPlatform('windows'))">$(TargetFrameworks);net9.0-windows10.0.19041.0</TargetFrameworks>

		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>

		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'ios'">11.0</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">21.0</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.17763.0</SupportedOSPlatformVersion>
		<TargetPlatformMinVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.17763.0</TargetPlatformMinVersion>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Acr.UserDialogs" Version="9.2.2" />
		<PackageReference Include="CommunityToolkit.Maui" Version="12.2.0" />
		
		<PackageReference Include="Microsoft.Maui.Controls" Version="9.0.100" />
		<PackageReference Include="Microsoft.Maui.Controls.Compatibility" Version="9.0.100" />
		<PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="9.0.8" />
		<PackageReference Include="SkiaSharp" Version="3.119.0" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<!--<PackageReference Include="Plugin.Firebase.Crashlytics" Version="3.1.1" />
		<PackageReference Include="Plugin.Firebase.CloudMessaging" Version="3.1.2" />-->
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\DeepMessage.Framework.Core\Platform.Framework.Core.csproj" />
	</ItemGroup>

</Project>
